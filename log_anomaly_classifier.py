#!/usr/bin/env python3
"""
DSPy-based Log Anomaly Classification Agent for BGL Dataset

This script implements a log anomaly classifier using DSPy with LabeledFewShot optimizer.
The classifier distinguishes between normal ("-") and anomalous (other characters) log entries
from the BGL (BlueGene/L) supercomputer dataset.
"""

import os
import re
import random
from typing import List, Tuple, Dict
from dataclasses import dataclass

import dspy
from dspy.datasets import Dataset


@dataclass
class LogEntry:
    """Represents a single log entry with its features and label"""
    raw_line: str
    label: str  # "normal" or "anomalous"
    timestamp: str
    node_id: str
    message: str


class LogPreprocessor:
    """Preprocesses BGL log entries to extract relevant features"""

    def __init__(self):
        pass
    
    def parse_log_line(self, line: str) -> LogEntry:
        """Parse a single BGL log line into structured components"""
        line = line.strip()
        if not line:
            return None
            
        # Extract label from first character
        label = "normal" if line[0] == '-' else "anomalous"
        
        # Split the line into parts
        parts = line.split()
        if len(parts) < 6:
            return LogEntry(
                raw_line=line,
                label=label,
                timestamp="",
                node_id="",
                message=line
            )
        
        # Extract components (BGL format: label timestamp date node_id time node_id_repeat component severity message...)
        timestamp = parts[1] if len(parts) > 1 else ""
        node_id = parts[3] if len(parts) > 3 else ""
        
        # Extract message (everything after the structured parts)
        message_start_idx = 6 if len(parts) > 6 else len(parts)
        message = " ".join(parts[message_start_idx:])

        return LogEntry(
            raw_line=line,
            label=label,
            timestamp=timestamp,
            node_id=node_id,
            message=message
        )
    



class LogClassificationSignature(dspy.Signature):
    """Signature for log anomaly classification task"""

    log_message = dspy.InputField(desc="The log message content to classify")
    node_id = dspy.InputField(desc="The node identifier where the log originated")

    classification = dspy.OutputField(desc="Classification as 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification decision")


class LogAnomalyClassifier(dspy.Module):
    """DSPy module for classifying log entries as normal or anomalous"""
    
    def __init__(self):
        super().__init__()
        self.classify = dspy.ChainOfThought(LogClassificationSignature)
    
    def forward(self, log_message: str, node_id: str):
        """Forward pass for log classification"""
        result = self.classify(
            log_message=log_message,
            node_id=node_id
        )
        return result


class BGLDataset:
    """Dataset loader and manager for BGL log data"""

    def __init__(self, file_path: str, max_samples: int = None, min_anomalous: int = 100, balanced_sampling: bool = True):
        self.file_path = file_path
        self.max_samples = max_samples
        self.min_anomalous = min_anomalous
        self.balanced_sampling = balanced_sampling
        self.preprocessor = LogPreprocessor()
        self.entries = []
        self._load_data()
    
    def _load_data(self):
        """Load and preprocess BGL log data with balanced sampling"""
        print(f"Loading BGL dataset from {self.file_path}")
        print(f"Balanced sampling: {self.balanced_sampling}, Min anomalous: {self.min_anomalous}")

        try:
            if self.balanced_sampling:
                self._load_balanced_data()
            else:
                self._load_sequential_data()

            print(f"Successfully loaded {len(self.entries)} log entries")

            # Print dataset statistics
            normal_count = sum(1 for entry in self.entries if entry.label == "normal")
            anomalous_count = len(self.entries) - normal_count
            print(f"Normal entries: {normal_count}")
            print(f"Anomalous entries: {anomalous_count}")

            if anomalous_count < self.min_anomalous:
                print(f"⚠️  Warning: Only {anomalous_count} anomalous entries found (minimum: {self.min_anomalous})")
                print("   Consider increasing max_samples or using balanced sampling")

        except Exception as e:
            print(f"Error loading dataset: {e}")
            raise

    def _load_sequential_data(self):
        """Load data sequentially (original method)"""
        with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
            count = 0
            for line in f:
                if self.max_samples and count >= self.max_samples:
                    break

                entry = self.preprocessor.parse_log_line(line)
                if entry:
                    self.entries.append(entry)
                    count += 1

                    if count % 10000 == 0:
                        print(f"Loaded {count} entries...")

    def _load_balanced_data(self):
        """Load data with balanced sampling to ensure sufficient anomalous examples"""
        normal_entries = []
        anomalous_entries = []

        # Calculate target counts
        if self.max_samples:
            target_anomalous = max(self.min_anomalous, self.max_samples // 4)  # At least 25% anomalous
            target_normal = self.max_samples - target_anomalous
        else:
            target_anomalous = self.min_anomalous * 2  # Collect more than minimum
            target_normal = target_anomalous * 3  # 3:1 ratio

        print(f"Target: {target_anomalous} anomalous, {target_normal} normal entries")

        with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
            line_count = 0
            for line in f:
                line_count += 1
                entry = self.preprocessor.parse_log_line(line)

                if entry:
                    if entry.label == "normal" and len(normal_entries) < target_normal:
                        normal_entries.append(entry)
                    elif entry.label == "anomalous" and len(anomalous_entries) < target_anomalous:
                        anomalous_entries.append(entry)

                # Progress reporting
                if line_count % 10000 == 0:
                    print(f"Processed {line_count} lines... Found Normal: {len(normal_entries)}, Anomalous: {len(anomalous_entries)}")

                # Stop when we have enough of both or reached max samples
                if len(normal_entries) >= target_normal and len(anomalous_entries) >= target_anomalous:
                    break

                # Safety check to avoid infinite loop
                if line_count > 1000000:  # Stop after 1M lines if targets not met
                    print(f"Reached 1M lines, stopping search")
                    break

        # Combine entries
        self.entries = normal_entries + anomalous_entries
        random.shuffle(self.entries)

        print(f"Balanced loading complete: {len(normal_entries)} normal, {len(anomalous_entries)} anomalous")

    def get_train_test_split(self, train_ratio: float = 0.8) -> Tuple[List[LogEntry], List[LogEntry]]:
        """Split dataset into training and testing sets"""
        random.shuffle(self.entries)
        split_idx = int(len(self.entries) * train_ratio)
        return self.entries[:split_idx], self.entries[split_idx:]
    
    def get_few_shot_examples(self, num_examples: int = 10) -> List[dspy.Example]:
        """Get few-shot examples for training"""
        examples = []
        
        # Get balanced examples
        normal_entries = [e for e in self.entries if e.label == "normal"]
        anomalous_entries = [e for e in self.entries if e.label == "anomalous"]
        
        # Sample equal numbers from each class
        num_per_class = num_examples // 2
        selected_normal = random.sample(normal_entries, min(num_per_class, len(normal_entries)))
        selected_anomalous = random.sample(anomalous_entries, min(num_per_class, len(anomalous_entries)))
        
        for entry in selected_normal + selected_anomalous:
            example = dspy.Example(
                log_message=entry.message,
                node_id=entry.node_id,
                classification=entry.label
            ).with_inputs("log_message", "node_id")
            examples.append(example)
        
        return examples


def evaluate_classifier(classifier: LogAnomalyClassifier, test_entries: List[LogEntry]) -> Dict[str, float]:
    """Evaluate the classifier on test data"""
    correct = 0
    total = len(test_entries)
    
    true_positives = 0
    false_positives = 0
    true_negatives = 0
    false_negatives = 0
    
    print("Evaluating classifier...")
    
    for i, entry in enumerate(test_entries):
        if i % 100 == 0:
            print(f"Evaluated {i}/{total} entries...")
        
        try:
            result = classifier(
                log_message=entry.message,
                node_id=entry.node_id
            )
            
            predicted = result.classification.lower().strip()
            actual = entry.label
            
            if predicted == actual:
                correct += 1
            
            # Calculate confusion matrix components
            if actual == "anomalous" and predicted == "anomalous":
                true_positives += 1
            elif actual == "normal" and predicted == "anomalous":
                false_positives += 1
            elif actual == "normal" and predicted == "normal":
                true_negatives += 1
            elif actual == "anomalous" and predicted == "normal":
                false_negatives += 1
                
        except Exception as e:
            print(f"Error classifying entry {i}: {e}")
            continue
    
    # Calculate metrics
    accuracy = correct / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    return {
        "accuracy": accuracy,
        "precision": precision,
        "recall": recall,
        "f1_score": f1_score,
        "true_positives": true_positives,
        "false_positives": false_positives,
        "true_negatives": true_negatives,
        "false_negatives": false_negatives
    }


def main():
    """Main function to run the log anomaly classification system"""
    
    # Configuration
    BGL_LOG_PATH = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
    MAX_SAMPLES = 5000  # Increased for better anomaly detection
    MIN_ANOMALOUS = 200  # Minimum anomalous examples needed
    
    print("=== DSPy Log Anomaly Classification System ===")
    
    # Initialize DSPy with Ollama
    try:
        from config import CURRENT_CONFIG as Config

        # Configure Ollama language model
        lm = dspy.LM(
            Config.MODEL_NAME,
            base_url=Config.get_ollama_url(),
            temperature=0.1
        )
        dspy.configure(lm=lm)
        print(f"Ollama language model configured successfully: {Config.MODEL_NAME}")
    except Exception as e:
        print(f"Error: Could not configure Ollama language model: {e}")
        print("Make sure Ollama is running and the model is available:")
        print(f"  ollama serve")
        print(f"  ollama pull {Config.MODEL_NAME}")
        return
    
    # Load dataset with balanced sampling
    dataset = BGLDataset(
        BGL_LOG_PATH,
        max_samples=MAX_SAMPLES,
        min_anomalous=MIN_ANOMALOUS,
        balanced_sampling=True
    )
    
    # Split data
    train_entries, test_entries = dataset.get_train_test_split(train_ratio=0.8)
    print(f"Training entries: {len(train_entries)}")
    print(f"Testing entries: {len(test_entries)}")
    
    # Get few-shot examples
    few_shot_examples = dataset.get_few_shot_examples(num_examples=20)
    print(f"Few-shot examples: {len(few_shot_examples)}")
    
    # Initialize classifier
    classifier = LogAnomalyClassifier()
    
    # Optimize with LabeledFewShot
    print("Optimizing classifier with LabeledFewShot...")
    optimizer = dspy.LabeledFewShot(k=len(few_shot_examples))
    optimized_classifier = optimizer.compile(classifier, trainset=few_shot_examples)
    
    # Evaluate the classifier
    print("Evaluating optimized classifier...")
    metrics = evaluate_classifier(optimized_classifier, test_entries[:100])  # Evaluate on subset for speed
    
    # Print results
    print("\n=== Evaluation Results ===")
    print(f"Accuracy: {metrics['accuracy']:.3f}")
    print(f"Precision: {metrics['precision']:.3f}")
    print(f"Recall: {metrics['recall']:.3f}")
    print(f"F1-Score: {metrics['f1_score']:.3f}")
    print(f"True Positives: {metrics['true_positives']}")
    print(f"False Positives: {metrics['false_positives']}")
    print(f"True Negatives: {metrics['true_negatives']}")
    print(f"False Negatives: {metrics['false_negatives']}")
    
    # Save the optimized classifier
    optimized_classifier.save("bgl_log_classifier.json")
    print("\nClassifier saved to bgl_log_classifier.json")
    
    return optimized_classifier


if __name__ == "__main__":
    main()
