#!/usr/bin/env python3
"""
Analyze the BGL dataset to understand class distribution and find optimal sampling strategy
"""

import os
import random
from collections import defaultdict, Counter
from typing import Dict, List, Tu<PERSON>

def analyze_bgl_distribution(file_path: str, sample_size: int = 50000) -> Dict:
    """Analyze the distribution of normal vs anomalous entries in BGL dataset"""
    
    print(f"Analyzing BGL dataset: {file_path}")
    print(f"Sampling up to {sample_size} entries for analysis...")
    
    normal_count = 0
    anomalous_count = 0
    total_lines = 0
    
    # Track anomalous patterns
    anomalous_patterns = Counter()
    anomalous_samples = []
    normal_samples = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f):
                line = line.strip()
                if not line:
                    continue
                
                total_lines += 1
                
                # Check if normal or anomalous
                if line.startswith('-'):
                    normal_count += 1
                    if len(normal_samples) < 100:  # Keep some samples
                        normal_samples.append(line)
                else:
                    anomalous_count += 1
                    if len(anomalous_samples) < 1000:  # Keep more anomalous samples
                        anomalous_samples.append(line)
                    
                    # Track the first character/pattern for anomalous entries
                    first_char = line[0] if line else ''
                    anomalous_patterns[first_char] += 1
                
                # Progress reporting
                if total_lines % 10000 == 0:
                    print(f"Processed {total_lines} lines... Normal: {normal_count}, Anomalous: {anomalous_count}")
                
                # Stop if we've reached our sample size
                if total_lines >= sample_size:
                    break
    
    except Exception as e:
        print(f"Error reading file: {e}")
        return {}
    
    # Calculate statistics
    anomaly_ratio = anomalous_count / total_lines if total_lines > 0 else 0
    
    results = {
        'total_lines': total_lines,
        'normal_count': normal_count,
        'anomalous_count': anomalous_count,
        'anomaly_ratio': anomaly_ratio,
        'anomalous_patterns': dict(anomalous_patterns),
        'normal_samples': normal_samples[:10],  # First 10 normal samples
        'anomalous_samples': anomalous_samples[:20]  # First 20 anomalous samples
    }
    
    return results


def find_balanced_sampling_strategy(file_path: str, target_anomalous: int = 500, target_normal: int = 500) -> Tuple[List[str], List[str]]:
    """Find a balanced sampling strategy to get enough anomalous and normal examples"""
    
    print(f"Finding balanced sampling strategy...")
    print(f"Target: {target_anomalous} anomalous, {target_normal} normal entries")
    
    normal_entries = []
    anomalous_entries = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f):
                line = line.strip()
                if not line:
                    continue
                
                if line.startswith('-'):
                    if len(normal_entries) < target_normal:
                        normal_entries.append(line)
                else:
                    if len(anomalous_entries) < target_anomalous:
                        anomalous_entries.append(line)
                
                # Stop when we have enough of both
                if len(normal_entries) >= target_normal and len(anomalous_entries) >= target_anomalous:
                    break
                
                # Progress reporting
                if line_num % 10000 == 0:
                    print(f"Processed {line_num} lines... Found Normal: {len(normal_entries)}, Anomalous: {len(anomalous_entries)}")
    
    except Exception as e:
        print(f"Error reading file: {e}")
        return [], []
    
    print(f"Found {len(normal_entries)} normal and {len(anomalous_entries)} anomalous entries")
    return normal_entries, anomalous_entries


def save_balanced_dataset(normal_entries: List[str], anomalous_entries: List[str], output_file: str):
    """Save a balanced dataset to a file"""
    
    print(f"Saving balanced dataset to {output_file}")
    
    # Combine and shuffle
    all_entries = normal_entries + anomalous_entries
    random.shuffle(all_entries)
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for entry in all_entries:
                f.write(entry + '\n')
        
        print(f"Saved {len(all_entries)} entries to {output_file}")
        print(f"  Normal: {len(normal_entries)}")
        print(f"  Anomalous: {len(anomalous_entries)}")
        
    except Exception as e:
        print(f"Error saving file: {e}")


def print_analysis_report(results: Dict):
    """Print a detailed analysis report"""
    
    print("\n" + "=" * 60)
    print("BGL Dataset Analysis Report")
    print("=" * 60)
    
    print(f"Total lines processed: {results['total_lines']:,}")
    print(f"Normal entries: {results['normal_count']:,} ({results['normal_count']/results['total_lines']*100:.1f}%)")
    print(f"Anomalous entries: {results['anomalous_count']:,} ({results['anomaly_ratio']*100:.1f}%)")
    
    print(f"\nClass imbalance ratio: {results['normal_count']/results['anomalous_count']:.1f}:1 (normal:anomalous)")
    
    print(f"\nAnomalous entry patterns:")
    for pattern, count in sorted(results['anomalous_patterns'].items(), key=lambda x: x[1], reverse=True):
        percentage = count / results['anomalous_count'] * 100
        print(f"  '{pattern}': {count:,} ({percentage:.1f}%)")
    
    print(f"\nSample normal entries:")
    for i, sample in enumerate(results['normal_samples'][:5], 1):
        print(f"  {i}. {sample[:80]}...")
    
    print(f"\nSample anomalous entries:")
    for i, sample in enumerate(results['anomalous_samples'][:5], 1):
        print(f"  {i}. {sample[:80]}...")
    
    # Recommendations
    print(f"\n" + "=" * 60)
    print("Recommendations for Training")
    print("=" * 60)
    
    if results['anomaly_ratio'] < 0.1:
        print("⚠️  Very low anomaly ratio detected!")
        print(f"   Consider using stratified sampling or oversampling techniques")
        print(f"   Recommended: Collect at least 1000 anomalous examples for training")
    
    if results['anomalous_count'] < 100:
        print("⚠️  Very few anomalous examples found!")
        print(f"   You may need to process more of the dataset")
    
    min_examples_needed = 50  # Minimum for few-shot learning
    if results['anomalous_count'] >= min_examples_needed:
        print(f"✅ Sufficient anomalous examples for few-shot learning ({results['anomalous_count']} >= {min_examples_needed})")
    else:
        print(f"❌ Insufficient anomalous examples for few-shot learning ({results['anomalous_count']} < {min_examples_needed})")


def main():
    """Main analysis function"""
    
    bgl_path = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
    
    if not os.path.exists(bgl_path):
        print(f"Error: BGL dataset not found at {bgl_path}")
        return
    
    # Step 1: Analyze overall distribution
    print("Step 1: Analyzing dataset distribution...")
    results = analyze_bgl_distribution(bgl_path, sample_size=100000)  # Analyze first 100k lines
    
    if not results:
        print("Failed to analyze dataset")
        return
    
    print_analysis_report(results)
    
    # Step 2: Create balanced dataset if needed
    if results['anomalous_count'] < 500:  # If we don't have enough anomalous examples
        print(f"\nStep 2: Searching for more anomalous examples...")
        normal_entries, anomalous_entries = find_balanced_sampling_strategy(
            bgl_path, 
            target_anomalous=1000,  # Try to find 1000 anomalous
            target_normal=1000      # And 1000 normal
        )
        
        if len(anomalous_entries) >= 100:  # Minimum viable
            balanced_file = "datasets/BGL/BGL_balanced.log"
            save_balanced_dataset(normal_entries, anomalous_entries, balanced_file)
            
            print(f"\n✅ Created balanced dataset: {balanced_file}")
            print(f"   Use this file for training by updating config.py:")
            print(f"   BGL_LOG_PATH = '{balanced_file}'")
        else:
            print(f"\n❌ Could not find enough anomalous examples ({len(anomalous_entries)} found)")
    else:
        print(f"\n✅ Sufficient anomalous examples found in sample")
    
    # Step 3: Provide configuration recommendations
    print(f"\n" + "=" * 60)
    print("Configuration Recommendations")
    print("=" * 60)
    
    recommended_samples = min(10000, results['total_lines'])
    recommended_few_shot = min(50, results['anomalous_count'] // 2)
    
    print(f"Recommended MAX_SAMPLES: {recommended_samples}")
    print(f"Recommended FEW_SHOT_EXAMPLES: {recommended_few_shot}")
    print(f"Recommended TRAIN_RATIO: 0.8")
    
    print(f"\nUpdate your config.py with:")
    print(f"  MAX_SAMPLES = {recommended_samples}")
    print(f"  FEW_SHOT_EXAMPLES = {recommended_few_shot}")


if __name__ == "__main__":
    main()
