#!/usr/bin/env python3
"""
Simple script to run dataset analysis and test the enhanced BGLDataset
"""

import sys
import os

def test_enhanced_dataset():
    """Test the enhanced BGLDataset with balanced sampling"""
    print("Testing Enhanced BGLDataset with Balanced Sampling")
    print("=" * 60)
    
    try:
        from log_anomaly_classifier import BGLDataset
        
        bgl_path = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
        
        if not os.path.exists(bgl_path):
            print(f"❌ BGL dataset not found at {bgl_path}")
            return False
        
        # Test with balanced sampling
        print("Loading dataset with balanced sampling...")
        dataset = BGLDataset(
            bgl_path,
            max_samples=2000,
            min_anomalous=100,
            balanced_sampling=True
        )
        
        if len(dataset.entries) == 0:
            print("❌ No entries loaded")
            return False
        
        # Analyze the results
        normal_count = sum(1 for entry in dataset.entries if entry.label == "normal")
        anomalous_count = len(dataset.entries) - normal_count
        
        print(f"\n📊 Dataset Statistics:")
        print(f"   Total entries: {len(dataset.entries)}")
        print(f"   Normal entries: {normal_count}")
        print(f"   Anomalous entries: {anomalous_count}")
        print(f"   Anomaly ratio: {anomalous_count/len(dataset.entries)*100:.1f}%")
        
        # Test train/test split
        train_entries, test_entries = dataset.get_train_test_split()
        print(f"\n📈 Train/Test Split:")
        print(f"   Training entries: {len(train_entries)}")
        print(f"   Testing entries: {len(test_entries)}")
        
        # Test few-shot examples
        few_shot_examples = dataset.get_few_shot_examples(num_examples=20)
        print(f"\n🎯 Few-shot Examples:")
        print(f"   Generated examples: {len(few_shot_examples)}")
        
        # Show sample entries
        print(f"\n📝 Sample Entries:")
        for i, entry in enumerate(dataset.entries[:5]):
            print(f"   {i+1}. [{entry.label.upper()}] {entry.severity} - {entry.message[:50]}...")
        
        # Check if we have enough anomalous examples
        if anomalous_count >= 100:
            print(f"\n✅ Sufficient anomalous examples for training ({anomalous_count} >= 100)")
            return True
        else:
            print(f"\n⚠️  Limited anomalous examples ({anomalous_count} < 100)")
            print("   Consider increasing max_samples or processing more of the dataset")
            return True  # Still successful, just a warning
            
    except Exception as e:
        print(f"❌ Error testing enhanced dataset: {e}")
        return False


def run_dataset_analysis():
    """Run the full dataset analysis"""
    print("\nRunning Full Dataset Analysis")
    print("=" * 60)
    
    try:
        from analyze_dataset import main as analyze_main
        analyze_main()
        return True
    except Exception as e:
        print(f"❌ Error running dataset analysis: {e}")
        return False


def main():
    """Main function"""
    print("🔍 BGL Dataset Analysis and Testing")
    print("=" * 60)
    
    # Test 1: Enhanced dataset loading
    test1_success = test_enhanced_dataset()
    
    # Test 2: Full dataset analysis (optional)
    print(f"\n" + "=" * 60)
    user_input = input("Run full dataset analysis? (y/N): ").strip().lower()
    
    if user_input in ['y', 'yes']:
        test2_success = run_dataset_analysis()
    else:
        test2_success = True
        print("Skipping full dataset analysis")
    
    # Summary
    print(f"\n" + "=" * 60)
    print("📋 Summary")
    print("=" * 60)
    
    if test1_success:
        print("✅ Enhanced dataset loading works correctly")
    else:
        print("❌ Enhanced dataset loading failed")
    
    if test2_success:
        print("✅ Dataset analysis completed successfully")
    else:
        print("❌ Dataset analysis failed")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Ready to train the classifier.")
        print("\n📋 Next steps:")
        print("1. Run: python log_anomaly_classifier.py")
        print("2. Or try: python demo_classifier.py")
    else:
        print("\n⚠️  Some issues detected. Please check the output above.")
    
    return test1_success and test2_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
