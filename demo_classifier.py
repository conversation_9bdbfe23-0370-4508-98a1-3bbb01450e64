#!/usr/bin/env python3
"""
Demo script for the DSPy Log Anomaly Classification System

This script demonstrates how to use the trained classifier to classify individual log entries.
"""

import json
import os
from typing import List, Dict

import dspy
from log_anomaly_classifier import LogAnomalyClassifier, LogPreprocessor, LogEntry
from config import CURRENT_CONFIG as Config


def load_trained_classifier(model_path: str) -> LogAnomalyClassifier:
    """Load a previously trained classifier"""
    if not os.path.exists(model_path):
        print(f"Error: Trained model not found at {model_path}")
        print("Please run log_anomaly_classifier.py first to train the model")
        return None
    
    classifier = LogAnomalyClassifier()
    try:
        classifier.load(model_path)
        print(f"Successfully loaded trained classifier from {model_path}")
        return classifier
    except Exception as e:
        print(f"Error loading classifier: {e}")
        return None


def classify_single_log(classifier: LogAnomalyClassifier, log_line: str) -> Dict:
    """Classify a single log entry"""
    preprocessor = LogPreprocessor()
    entry = preprocessor.parse_log_line(log_line)
    
    if not entry:
        return {"error": "Could not parse log line"}
    
    try:
        result = classifier(
            log_message=entry.message,
            node_id=entry.node_id
        )
        
        return {
            "original_line": log_line.strip(),
            "parsed_message": entry.message,
            "node_id": entry.node_id,
            "predicted_classification": result.classification,
            "reasoning": result.reasoning,
            "actual_label": entry.label
        }
    except Exception as e:
        return {"error": f"Classification failed: {e}"}


def demo_with_sample_logs():
    """Demonstrate classification with sample log entries"""
    
    # Configure DSPy
    if not Config.validate_config():
        print("Configuration validation failed. Please check your settings.")
        return
    
    try:
        lm = dspy.LM(
            Config.MODEL_NAME,
            base_url=Config.get_ollama_url(),
            temperature=0.1
        )
        dspy.configure(lm=lm)
        print(f"Ollama language model configured successfully: {Config.MODEL_NAME}")
    except Exception as e:
        print(f"Error configuring Ollama language model: {e}")
        return
    
    # Load trained classifier
    classifier = load_trained_classifier(Config.MODEL_SAVE_PATH)
    if not classifier:
        print("Creating new classifier for demonstration...")
        classifier = LogAnomalyClassifier()
    
    # Sample log entries for demonstration
    sample_logs = [
        # Normal log entries (start with -)
        "- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected",
        "- 1117838571 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.51.131467 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected",
        "- 1117838572 2005.06.03 R02-M1-N1-C:J13-U12 2005-06-03-15.43.01.234567 R02-M1-N1-C:J13-U12 APP INFO application started successfully",
        
        # Anomalous log entries (don't start with -)
        "FATAL 1117838580 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.45.30.123456 R02-M1-N0-C:J12-U11 RAS KERNEL FATAL system failure detected",
        "ERROR 1117838590 2005.06.03 R02-M1-N2-C:J14-U13 2005-06-03-15.46.15.789012 R02-M1-N2-C:J14-U13 MMCS ERROR memory allocation failed",
        "WARN 1117838600 2005.06.03 R02-M1-N3-C:J15-U14 2005-06-03-15.47.00.345678 R02-M1-N3-C:J15-U14 DISCOVERY WARN network timeout occurred"
    ]
    
    print("\n=== Log Anomaly Classification Demo ===")
    print(f"Classifying {len(sample_logs)} sample log entries...\n")
    
    results = []
    for i, log_line in enumerate(sample_logs, 1):
        print(f"--- Sample {i} ---")
        result = classify_single_log(classifier, log_line)
        
        if "error" in result:
            print(f"Error: {result['error']}")
        else:
            print(f"Original: {result['original_line'][:100]}...")
            print(f"Message: {result['parsed_message'][:80]}...")
            print(f"Node ID: {result['node_id']}")
            print(f"Actual Label: {result['actual_label']}")
            print(f"Predicted: {result['predicted_classification']}")
            print(f"Reasoning: {result['reasoning']}")
            
            # Check if prediction matches actual
            is_correct = result['predicted_classification'].lower() == result['actual_label'].lower()
            print(f"Correct: {'✓' if is_correct else '✗'}")
        
        results.append(result)
        print()
    
    # Calculate accuracy
    correct_predictions = sum(1 for r in results 
                            if "error" not in r and 
                            r['predicted_classification'].lower() == r['actual_label'].lower())
    total_predictions = len([r for r in results if "error" not in r])
    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    
    print(f"=== Demo Results ===")
    print(f"Total samples: {len(sample_logs)}")
    print(f"Successful predictions: {total_predictions}")
    print(f"Correct predictions: {correct_predictions}")
    print(f"Accuracy: {accuracy:.2%}")
    
    # Save results
    with open("demo_results.json", "w") as f:
        json.dump(results, f, indent=2)
    print(f"Results saved to demo_results.json")


def interactive_classification():
    """Interactive mode for classifying user-provided log entries"""
    
    # Configure DSPy
    if not Config.validate_config():
        print("Configuration validation failed. Please check your settings.")
        return
    
    try:
        lm = dspy.LM(
            Config.MODEL_NAME,
            base_url=Config.get_ollama_url(),
            temperature=0.1
        )
        dspy.configure(lm=lm)
    except Exception as e:
        print(f"Error configuring Ollama language model: {e}")
        return
    
    # Load classifier
    classifier = load_trained_classifier(Config.MODEL_SAVE_PATH)
    if not classifier:
        print("No trained classifier found. Please train the model first.")
        return
    
    print("\n=== Interactive Log Classification ===")
    print("Enter log entries to classify (type 'quit' to exit):")
    
    while True:
        try:
            log_line = input("\nEnter log entry: ").strip()
            if log_line.lower() in ['quit', 'exit', 'q']:
                break
            
            if not log_line:
                continue
            
            result = classify_single_log(classifier, log_line)

            if "error" in result:
                print(f"Error: {result['error']}")
            else:
                print(f"Classification: {result['predicted_classification']}")
                print(f"Reasoning: {result['reasoning']}")
                print(f"Parsed components:")
                print(f"  - Node ID: {result['node_id']}")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("Goodbye!")


def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_classification()
    else:
        demo_with_sample_logs()


if __name__ == "__main__":
    main()
