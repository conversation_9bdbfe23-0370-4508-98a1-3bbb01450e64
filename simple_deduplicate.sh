#!/bin/bash

# Simple BGL Log Deduplication Script
# No external dependencies required

set -e

# Configuration
INPUT_FILE="datasets/BGL/BGL.log"
OUTPUT_FILE="datasets/BGL/BGL_deduplicated.log"

echo "=== BGL Log Deduplication (After 10th Space) ==="
echo

# Check if input file exists
if [ ! -f "$INPUT_FILE" ]; then
    echo "ERROR: Input file not found: $INPUT_FILE"
    exit 1
fi

echo "Input file: $INPUT_FILE"

# Get original line count
echo "Counting original lines..."
ORIGINAL_LINES=$(wc -l < "$INPUT_FILE")
echo "Original lines: $ORIGINAL_LINES"

# Create output directory
mkdir -p "$(dirname "$OUTPUT_FILE")"

# Perform deduplication based on content after 10th space (preserves order)
echo "Removing duplicate lines based on message content after 10th space..."
awk '{
    # Find the position after the 10th space
    spaces = 0
    pos = 1
    for (i = 1; i <= length($0); i++) {
        if (substr($0, i, 1) == " ") {
            spaces++
            if (spaces == 10) {
                pos = i + 1
                break
            }
        }
    }

    # Extract the message part (everything after 10th space)
    if (spaces >= 10) {
        message = substr($0, pos)
    } else {
        message = $0  # Use full line if less than 10 spaces
    }

    # Use first character (alert type) + message as deduplication key
    key = substr($0, 1, 1) " " message

    if (!seen[key]++) {
        print $0
    }
}' "$INPUT_FILE" > "$OUTPUT_FILE"

# Get deduplicated line count
DEDUPLICATED_LINES=$(wc -l < "$OUTPUT_FILE")
REMOVED_LINES=$((ORIGINAL_LINES - DEDUPLICATED_LINES))

echo
echo "=== RESULTS ==="
echo "Original lines:      $ORIGINAL_LINES"
echo "Deduplicated lines:  $DEDUPLICATED_LINES"
echo "Removed lines:       $REMOVED_LINES"
echo "Output file:         $OUTPUT_FILE"

# Calculate percentage (using integer arithmetic)
if [ $ORIGINAL_LINES -gt 0 ]; then
    REDUCTION_PCT=$((REMOVED_LINES * 100 / ORIGINAL_LINES))
    echo "Reduction:           $REDUCTION_PCT%"
fi

echo
echo "SUCCESS: Deduplication completed!"
echo
echo "Next steps:"
echo "1. Update config.py to use deduplicated file:"
echo "   BGL_LOG_PATH = '$OUTPUT_FILE'"
echo "2. Run the classifier:"
echo "   python log_anomaly_classifier.py"
