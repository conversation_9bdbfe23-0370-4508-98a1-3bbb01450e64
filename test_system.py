#!/usr/bin/env python3
"""
Test script to verify the log anomaly classification system components
"""

import os
import sys
from typing import List

def test_dataset_access():
    """Test if we can access the BGL dataset"""
    print("=== Testing Dataset Access ===")
    
    bgl_path = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
    
    if not os.path.exists(bgl_path):
        print(f"❌ BGL dataset not found at {bgl_path}")
        return False
    
    try:
        with open(bgl_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = []
            for i, line in enumerate(f):
                lines.append(line.strip())
                if i >= 10:  # Read first 10 lines
                    break
        
        print(f"✅ Successfully read {len(lines)} lines from BGL dataset")
        
        # Analyze the lines
        normal_count = sum(1 for line in lines if line.startswith('-'))
        anomalous_count = len(lines) - normal_count
        
        print(f"   Normal entries (start with '-'): {normal_count}")
        print(f"   Anomalous entries: {anomalous_count}")
        
        # Show sample entries
        print("\n   Sample entries:")
        for i, line in enumerate(lines[:3]):
            label = "NORMAL" if line.startswith('-') else "ANOMALOUS"
            print(f"   {i+1}. [{label}] {line[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading BGL dataset: {e}")
        return False


def test_log_preprocessing():
    """Test the log preprocessing functionality"""
    print("\n=== Testing Log Preprocessing ===")
    
    try:
        # Import our preprocessor
        sys.path.append('.')
        from log_anomaly_classifier import LogPreprocessor
        
        preprocessor = LogPreprocessor()
        
        # Test with sample log lines
        sample_lines = [
            "- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected",
            "FATAL 1117838580 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.45.30.123456 R02-M1-N0-C:J12-U11 RAS KERNEL FATAL system failure detected"
        ]
        
        print("✅ LogPreprocessor imported successfully")
        
        for i, line in enumerate(sample_lines):
            entry = preprocessor.parse_log_line(line)
            if entry:
                print(f"\n   Sample {i+1}:")
                print(f"   Label: {entry.label}")
                print(f"   Node ID: {entry.node_id}")
                print(f"   Severity: {entry.severity}")
                print(f"   Component: {entry.component}")
                print(f"   Message: {entry.message[:50]}...")
            else:
                print(f"   ❌ Failed to parse line {i+1}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import LogPreprocessor: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing preprocessing: {e}")
        return False


def test_dspy_availability():
    """Test if DSPy is available"""
    print("\n=== Testing DSPy Availability ===")
    
    try:
        import dspy
        print(f"✅ DSPy imported successfully")
        print(f"   Version: {getattr(dspy, '__version__', 'Unknown')}")
        return True
    except ImportError:
        print("❌ DSPy not available")
        print("   Install with: pip install dspy-ai")
        return False
    except Exception as e:
        print(f"❌ Error importing DSPy: {e}")
        return False


def test_ollama_config():
    """Test Ollama configuration"""
    print("\n=== Testing Ollama Configuration ===")

    try:
        import requests

        # Test Ollama connection
        ollama_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        response = requests.get(f"{ollama_url}/api/tags", timeout=5)

        if response.status_code == 200:
            print(f"✅ Ollama is running at {ollama_url}")

            # Check available models
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]

            target_model = "unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Q2_K_XL"
            if any(target_model in name for name in model_names):
                print(f"✅ Target model {target_model} is available")
                return True
            else:
                print(f"❌ Target model {target_model} not found")
                print(f"   Available models: {model_names[:3]}...")
                print(f"   Pull with: ollama pull {target_model}")
                return False
        else:
            print(f"❌ Ollama not responding at {ollama_url}")
            print("   Start with: ollama serve")
            return False

    except requests.exceptions.RequestException:
        print("❌ Could not connect to Ollama")
        print("   Make sure Ollama is running: ollama serve")
        return False
    except ImportError:
        print("❌ requests library not available")
        print("   Install with: pip install requests")
        return False
    except Exception as e:
        print(f"❌ Error testing Ollama: {e}")
        return False


def test_file_structure():
    """Test if all required files are present"""
    print("\n=== Testing File Structure ===")
    
    required_files = [
        "log_anomaly_classifier.py",
        "demo_classifier.py", 
        "config.py",
        "requirements.txt",
        "README.md"
    ]
    
    all_present = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - Missing")
            all_present = False
    
    return all_present


def run_basic_demo():
    """Run a basic demo without DSPy to test core functionality"""
    print("\n=== Running Basic Demo (No DSPy) ===")
    
    try:
        from log_anomaly_classifier import LogPreprocessor, BGLDataset
        
        # Test dataset loading with small sample
        print("Loading small dataset sample...")
        dataset = BGLDataset(
            "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log",
            max_samples=500,
            min_anomalous=50,
            balanced_sampling=True
        )
        
        if len(dataset.entries) > 0:
            print(f"✅ Loaded {len(dataset.entries)} entries")
            
            # Show statistics
            normal_count = sum(1 for entry in dataset.entries if entry.label == "normal")
            anomalous_count = len(dataset.entries) - normal_count
            print(f"   Normal: {normal_count}, Anomalous: {anomalous_count}")
            
            # Show sample entries
            print("\n   Sample processed entries:")
            for i, entry in enumerate(dataset.entries[:3]):
                print(f"   {i+1}. [{entry.label.upper()}] Node: {entry.node_id}, Severity: {entry.severity}")
                print(f"      Message: {entry.message[:60]}...")
            
            return True
        else:
            print("❌ No entries loaded")
            return False
            
    except Exception as e:
        print(f"❌ Error in basic demo: {e}")
        return False


def main():
    """Run all tests"""
    print("🔍 Testing DSPy Log Anomaly Classification System")
    print("=" * 60)
    
    tests = [
        ("Dataset Access", test_dataset_access),
        ("Log Preprocessing", test_log_preprocessing),
        ("DSPy Availability", test_dspy_availability),
        ("Ollama Configuration", test_ollama_config),
        ("File Structure", test_file_structure),
        ("Basic Demo", run_basic_demo)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 Test Summary")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
    else:
        print("⚠️  Some tests failed. Please address the issues above.")
        
        # Provide next steps
        print("\n📋 Next Steps:")
        if not any(name == "DSPy Availability" and result for name, result in results):
            print("1. Install DSPy: pip install dspy-ai")
        if not any(name == "Ollama Configuration" and result for name, result in results):
            print("2. Start Ollama: ollama serve")
            print("3. Pull model: ollama pull unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Q2_K_XL")
        print("4. Run the main classifier: python log_anomaly_classifier.py")


if __name__ == "__main__":
    main()
