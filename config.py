#!/usr/bin/env python3
"""
Configuration file for the DSPy Log Anomaly Classification System
"""

import os
from typing import Optional

class Config:
    """Configuration class for the log anomaly classifier"""
    
    # Dataset configuration
    BGL_LOG_PATH = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
    MAX_SAMPLES = 5000  # Increase for full dataset processing
    TRAIN_RATIO = 0.8
    FEW_SHOT_EXAMPLES = 20
    
    # Model configuration
    MODEL_NAME = "gpt-3.5-turbo"  # Change to your preferred model
    API_KEY = None  # Set your API key here or use environment variable
    
    # Evaluation configuration
    EVAL_BATCH_SIZE = 100
    
    # Output configuration
    MODEL_SAVE_PATH = "bgl_log_classifier.json"
    RESULTS_SAVE_PATH = "classification_results.json"
    
    @classmethod
    def get_api_key(cls) -> Optional[str]:
        """Get API key from config or environment variable"""
        return cls.API_KEY or os.getenv("OPENAI_API_KEY")
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate configuration settings"""
        if not os.path.exists(cls.BGL_LOG_PATH):
            print(f"Error: BGL log file not found at {cls.BGL_LOG_PATH}")
            return False
        
        if not cls.get_api_key():
            print("Warning: No API key configured. Set OPENAI_API_KEY environment variable or update config.py")
            return False
        
        return True


# Alternative configurations for different scenarios
class DevelopmentConfig(Config):
    """Configuration for development/testing"""
    MAX_SAMPLES = 1000
    FEW_SHOT_EXAMPLES = 10
    EVAL_BATCH_SIZE = 50


class ProductionConfig(Config):
    """Configuration for production use"""
    MAX_SAMPLES = None  # Use full dataset
    FEW_SHOT_EXAMPLES = 50
    EVAL_BATCH_SIZE = 500


# Select configuration
CURRENT_CONFIG = DevelopmentConfig  # Change to ProductionConfig for full runs
