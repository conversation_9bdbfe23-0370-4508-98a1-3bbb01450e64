#!/usr/bin/env python3
"""
Configuration file for the DSPy Log Anomaly Classification System
"""

import os
from typing import Optional

class Config:
    """Configuration class for the log anomaly classifier"""
    
    # Dataset configuration
    BGL_LOG_PATH = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
    MAX_SAMPLES = 5000  # Increase for full dataset processing
    TRAIN_RATIO = 0.8
    FEW_SHOT_EXAMPLES = 20
    
    # Model configuration
    MODEL_NAME = "ollama_chat/hf.co/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Q2_K_XL"  # Ollama model
    OLLAMA_BASE_URL = "http://localhost:11434"  # Default Ollama URL
    API_KEY = None  # Not needed for Ollama
    
    # Evaluation configuration
    EVAL_BATCH_SIZE = 100
    
    # Output configuration
    MODEL_SAVE_PATH = "bgl_log_classifier.json"
    RESULTS_SAVE_PATH = "classification_results.json"
    
    @classmethod
    def get_ollama_url(cls) -> str:
        """Get Ollama base URL from config or environment variable"""
        return os.getenv("OLLAMA_BASE_URL", cls.OLLAMA_BASE_URL)

    @classmethod
    def validate_config(cls) -> bool:
        """Validate configuration settings"""
        if not os.path.exists(cls.BGL_LOG_PATH):
            print(f"Error: BGL log file not found at {cls.BGL_LOG_PATH}")
            return False

        # Test Ollama connection
        try:
            import requests
            response = requests.get(f"{cls.get_ollama_url()}/api/tags", timeout=5)
            if response.status_code != 200:
                print(f"Warning: Could not connect to Ollama at {cls.get_ollama_url()}")
                print("Make sure Ollama is running: ollama serve")
                return False
        except Exception as e:
            print(f"Warning: Could not connect to Ollama: {e}")
            print("Make sure Ollama is running: ollama serve")
            return False

        return True


# Alternative configurations for different scenarios
class DevelopmentConfig(Config):
    """Configuration for development/testing"""
    MAX_SAMPLES = 1000
    FEW_SHOT_EXAMPLES = 10
    EVAL_BATCH_SIZE = 50


class ProductionConfig(Config):
    """Configuration for production use"""
    MAX_SAMPLES = None  # Use full dataset
    FEW_SHOT_EXAMPLES = 50
    EVAL_BATCH_SIZE = 500


# Select configuration
CURRENT_CONFIG = DevelopmentConfig  # Change to ProductionConfig for full runs
