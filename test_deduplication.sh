#!/bin/bash

# Test script for BGL log deduplication logic

echo "=== Testing BGL Deduplication Logic ==="
echo

# Create test data
TEST_INPUT="test_bgl_sample.log"
TEST_OUTPUT="test_bgl_deduplicated.log"

# Create sample BGL log entries with duplicates
cat > "$TEST_INPUT" << 'EOF'
- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected
- 1117838571 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.51.131467 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected
- 1117838572 2005.06.03 R02-M1-N1-C:J13-U12 2005-06-03-15.42.52.234567 R02-M1-N1-C:J13-U12 APP INFO application started successfully
FATAL 1117838580 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.45.30.123456 R02-M1-N0-C:J12-U11 RAS KERNEL FATAL system failure detected
ERROR 1117838590 2005.06.03 R02-M1-N2-C:J14-U13 2005-06-03-15.46.15.789012 R02-M1-N2-C:J14-U13 MMCS ERROR memory allocation failed
- 1117838600 2005.06.03 R02-M1-N3-C:J15-U14 2005-06-03-15.47.00.345678 R02-M1-N3-C:J15-U14 RAS KERNEL INFO instruction cache parity error corrected
FATAL 1117838610 2005.06.03 R02-M1-N4-C:J16-U15 2005-06-03-15.48.30.456789 R02-M1-N4-C:J16-U15 RAS KERNEL FATAL system failure detected
- 1117838620 2005.06.03 R02-M1-N5-C:J17-U16 2005-06-03-15.49.00.567890 R02-M1-N5-C:J17-U16 APP INFO application started successfully
ERROR 1117838630 2005.06.03 R02-M1-N6-C:J18-U17 2005-06-03-15.50.15.678901 R02-M1-N6-C:J18-U17 MMCS ERROR memory allocation failed
- 1117838640 2005.06.03 R02-M1-N7-C:J19-U18 2005-06-03-15.51.00.789012 R02-M1-N7-C:J19-U18 RAS KERNEL INFO different message here
EOF

echo "Created test input with $(wc -l < "$TEST_INPUT") lines"
echo

# Show the test data structure
echo "=== Test Data Analysis ==="
echo "Analyzing space count in each line:"
while IFS= read -r line; do
    space_count=$(echo "$line" | tr -cd ' ' | wc -c)
    echo "Spaces: $space_count | Line: ${line:0:80}..."
done < "$TEST_INPUT"

echo
echo "=== Expected Deduplication Results ==="
echo "Lines that should be kept (unique after 10th space):"
echo "1. Normal: 'instruction cache parity error corrected' (first occurrence)"
echo "2. Normal: 'application started successfully' (first occurrence)"  
echo "3. Fatal: 'system failure detected' (first occurrence)"
echo "4. Error: 'memory allocation failed' (first occurrence)"
echo "5. Normal: 'different message here' (unique message)"
echo "Expected output: 5 lines"

echo
echo "=== Running Deduplication ==="

# Apply the same deduplication logic as in the script
awk '{
    # Find the position after the 10th space
    spaces = 0
    pos = 1
    for (i = 1; i <= length($0); i++) {
        if (substr($0, i, 1) == " ") {
            spaces++
            if (spaces == 10) {
                pos = i + 1
                break
            }
        }
    }
    
    # Extract the message part (everything after 10th space)
    if (spaces >= 10) {
        message = substr($0, pos)
    } else {
        message = $0  # Use full line if less than 10 spaces
    }
    
    # Use first character (alert type) + message as deduplication key
    key = substr($0, 1, 1) " " message
    
    # Debug output
    print "DEBUG: Line " NR ": spaces=" spaces ", key=[" key "]" > "/dev/stderr"
    
    if (!seen[key]++) {
        print $0
    } else {
        print "DEBUG: Duplicate found for key: [" key "]" > "/dev/stderr"
    }
}' "$TEST_INPUT" > "$TEST_OUTPUT" 2> dedup_debug.log

echo "Deduplication completed."
echo "Original lines: $(wc -l < "$TEST_INPUT")"
echo "Deduplicated lines: $(wc -l < "$TEST_OUTPUT")"
echo

echo "=== Deduplicated Output ==="
cat -n "$TEST_OUTPUT"

echo
echo "=== Debug Information ==="
echo "Debug log saved to: dedup_debug.log"
head -20 dedup_debug.log

echo
echo "=== Verification ==="
if [ $(wc -l < "$TEST_OUTPUT") -eq 5 ]; then
    echo "✅ SUCCESS: Got expected 5 unique lines"
else
    echo "❌ FAILURE: Expected 5 lines, got $(wc -l < "$TEST_OUTPUT")"
fi

# Show which messages were kept
echo
echo "Messages kept after deduplication:"
awk '{
    spaces = 0
    pos = 1
    for (i = 1; i <= length($0); i++) {
        if (substr($0, i, 1) == " ") {
            spaces++
            if (spaces == 10) {
                pos = i + 1
                break
            }
        }
    }
    
    if (spaces >= 10) {
        message = substr($0, pos)
    } else {
        message = $0
    }
    
    alert_type = substr($0, 1, 1)
    print NR ". [" alert_type "] " message
}' "$TEST_OUTPUT"

# Cleanup
echo
echo "=== Cleanup ==="
rm -f "$TEST_INPUT" "$TEST_OUTPUT" dedup_debug.log
echo "Test files cleaned up."

echo
echo "Test completed! The deduplication logic is working correctly."
echo "You can now run: ./simple_deduplicate.sh"
