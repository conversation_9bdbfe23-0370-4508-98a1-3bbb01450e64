#!/usr/bin/env python3
"""
Test the simplified log anomaly classifier (without severity and component parsing)
"""

import sys
import os

def test_log_parsing():
    """Test the simplified log parsing"""
    print("Testing Simplified Log Parsing")
    print("=" * 50)
    
    try:
        from log_anomaly_classifier import LogPreprocessor
        
        preprocessor = LogPreprocessor()
        
        # Test sample log lines
        test_lines = [
            "- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected",
            "FATAL 1117838580 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.45.30.123456 R02-M1-N0-C:J12-U11 RAS KERNEL FATAL system failure detected",
            "ERROR 1117838590 2005.06.03 R02-M1-N2-C:J14-U13 2005-06-03-15.46.15.789012 R02-M1-N2-C:J14-U13 MMCS ERROR memory allocation failed"
        ]
        
        for i, line in enumerate(test_lines):
            print(f"\nTest {i+1}:")
            print(f"Input: {line[:80]}...")
            
            entry = preprocessor.parse_log_line(line)
            if entry:
                print(f"✅ Parsed successfully:")
                print(f"   Label: {entry.label}")
                print(f"   Node ID: {entry.node_id}")
                print(f"   Timestamp: {entry.timestamp}")
                print(f"   Message: {entry.message[:50]}...")
            else:
                print(f"❌ Failed to parse")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in log parsing test: {e}")
        return False


def test_dataset_loading():
    """Test the enhanced dataset loading with balanced sampling"""
    print("\nTesting Enhanced Dataset Loading")
    print("=" * 50)
    
    try:
        from log_anomaly_classifier import BGLDataset
        
        bgl_path = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
        
        if not os.path.exists(bgl_path):
            print(f"❌ BGL dataset not found at {bgl_path}")
            return False
        
        # Test with balanced sampling
        print("Loading dataset with balanced sampling...")
        dataset = BGLDataset(
            bgl_path,
            max_samples=1000,
            min_anomalous=50,
            balanced_sampling=True
        )
        
        if len(dataset.entries) == 0:
            print("❌ No entries loaded")
            return False
        
        # Analyze the results
        normal_count = sum(1 for entry in dataset.entries if entry.label == "normal")
        anomalous_count = len(dataset.entries) - normal_count
        
        print(f"\n📊 Dataset Statistics:")
        print(f"   Total entries: {len(dataset.entries)}")
        print(f"   Normal entries: {normal_count}")
        print(f"   Anomalous entries: {anomalous_count}")
        print(f"   Anomaly ratio: {anomalous_count/len(dataset.entries)*100:.1f}%")
        
        # Test few-shot examples
        few_shot_examples = dataset.get_few_shot_examples(num_examples=10)
        print(f"\n🎯 Few-shot Examples:")
        print(f"   Generated examples: {len(few_shot_examples)}")
        
        # Show sample entries
        print(f"\n📝 Sample Entries:")
        for i, entry in enumerate(dataset.entries[:3]):
            print(f"   {i+1}. [{entry.label.upper()}] {entry.node_id}")
            print(f"      Message: {entry.message[:60]}...")
        
        # Check if we have enough anomalous examples
        if anomalous_count >= 20:
            print(f"\n✅ Sufficient anomalous examples for testing ({anomalous_count} >= 20)")
            return True
        else:
            print(f"\n⚠️  Limited anomalous examples ({anomalous_count} < 20)")
            print("   Consider increasing max_samples")
            return True  # Still successful, just a warning
            
    except Exception as e:
        print(f"❌ Error in dataset loading test: {e}")
        return False


def test_classification_signature():
    """Test the simplified classification signature"""
    print("\nTesting Classification Signature")
    print("=" * 50)
    
    try:
        from log_anomaly_classifier import LogClassificationSignature, LogAnomalyClassifier
        
        # Test signature creation
        classifier = LogAnomalyClassifier()
        print("✅ LogAnomalyClassifier created successfully")
        
        # Check signature fields
        signature = LogClassificationSignature
        input_fields = [field for field in dir(signature) if not field.startswith('_')]
        print(f"✅ Signature fields available: {len(input_fields)} fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in signature test: {e}")
        return False


def test_few_shot_examples():
    """Test few-shot example generation"""
    print("\nTesting Few-shot Example Generation")
    print("=" * 50)
    
    try:
        from log_anomaly_classifier import BGLDataset
        
        bgl_path = "/home/<USER>/Documents/log-anomaly/datasets/BGL/BGL.log"
        
        if not os.path.exists(bgl_path):
            print(f"❌ BGL dataset not found")
            return False
        
        # Load small dataset
        dataset = BGLDataset(
            bgl_path,
            max_samples=500,
            min_anomalous=25,
            balanced_sampling=True
        )
        
        # Generate few-shot examples
        examples = dataset.get_few_shot_examples(num_examples=10)
        
        print(f"Generated {len(examples)} few-shot examples")
        
        # Check example structure
        if len(examples) > 0:
            example = examples[0]
            print(f"\nSample example structure:")
            print(f"   Inputs: {example.inputs}")
            print(f"   Log message: {example.log_message[:50]}...")
            print(f"   Node ID: {example.node_id}")
            print(f"   Classification: {example.classification}")
            
            # Count normal vs anomalous examples
            normal_examples = sum(1 for ex in examples if ex.classification == "normal")
            anomalous_examples = len(examples) - normal_examples
            
            print(f"\nExample distribution:")
            print(f"   Normal examples: {normal_examples}")
            print(f"   Anomalous examples: {anomalous_examples}")
            
            if anomalous_examples > 0:
                print("✅ Few-shot examples include both normal and anomalous cases")
                return True
            else:
                print("⚠️  No anomalous examples in few-shot set")
                return True
        else:
            print("❌ No few-shot examples generated")
            return False
            
    except Exception as e:
        print(f"❌ Error in few-shot test: {e}")
        return False


def main():
    """Run all simplified tests"""
    print("🧪 Testing Simplified Log Anomaly Classifier")
    print("=" * 60)
    
    tests = [
        ("Log Parsing", test_log_parsing),
        ("Dataset Loading", test_dataset_loading),
        ("Classification Signature", test_classification_signature),
        ("Few-shot Examples", test_few_shot_examples)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Simplified system is working correctly.")
        print("\n📋 The system now uses:")
        print("   - Log message content")
        print("   - Node ID")
        print("   - No severity or component parsing")
        print("\n📋 Next steps:")
        print("1. Install DSPy: pip install dspy-ai")
        print("2. Start Ollama and pull model")
        print("3. Run: python log_anomaly_classifier.py")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
