#!/usr/bin/env python3
"""
Quick test script to verify the DSPy log anomaly classifier with Ollama
"""

import sys
import os

def test_imports():
    """Test if all required imports work"""
    print("Testing imports...")
    
    try:
        import dspy
        print("✅ DSPy imported successfully")
    except ImportError as e:
        print(f"❌ DSPy import failed: {e}")
        return False
    
    try:
        import requests
        print("✅ Requests imported successfully")
    except ImportError as e:
        print(f"❌ Requests import failed: {e}")
        return False
    
    try:
        from log_anomaly_classifier import LogPreprocessor, LogAnomalyClassifier
        print("✅ Log classifier components imported successfully")
    except ImportError as e:
        print(f"❌ Log classifier import failed: {e}")
        return False
    
    return True


def test_ollama_connection():
    """Test Ollama connection"""
    print("\nTesting Ollama connection...")
    
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        
        if response.status_code == 200:
            print("✅ Ollama is running")
            
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]
            
            target_model = "unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Q2_K_XL"
            if any(target_model in name for name in model_names):
                print(f"✅ Target model available")
                return True
            else:
                print(f"❌ Target model not found")
                print(f"Available models: {model_names}")
                return False
        else:
            print("❌ Ollama not responding")
            return False
            
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False


def test_dspy_ollama():
    """Test DSPy with Ollama"""
    print("\nTesting DSPy with Ollama...")
    
    try:
        import dspy
        from config import CURRENT_CONFIG as Config
        
        # Configure Ollama
        lm = dspy.LM(
            Config.MODEL_NAME,
            base_url=Config.get_ollama_url(),
            temperature=0.1
        )
        dspy.configure(lm=lm)
        print("✅ DSPy configured with Ollama")
        
        # Test simple generation
        class SimpleSignature(dspy.Signature):
            question = dspy.InputField()
            answer = dspy.OutputField()
        
        generator = dspy.Predict(SimpleSignature)
        result = generator(question="What is 2+2?")
        
        print(f"✅ Simple test successful: {result.answer[:50]}...")
        return True
        
    except Exception as e:
        print(f"❌ DSPy-Ollama test failed: {e}")
        return False


def test_log_preprocessing():
    """Test log preprocessing"""
    print("\nTesting log preprocessing...")
    
    try:
        from log_anomaly_classifier import LogPreprocessor
        
        preprocessor = LogPreprocessor()
        
        # Test sample log lines
        test_lines = [
            "- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected",
            "FATAL 1117838580 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.45.30.123456 R02-M1-N0-C:J12-U11 RAS KERNEL FATAL system failure detected"
        ]
        
        for i, line in enumerate(test_lines):
            entry = preprocessor.parse_log_line(line)
            if entry:
                print(f"✅ Parsed line {i+1}: {entry.label} - {entry.severity}")
            else:
                print(f"❌ Failed to parse line {i+1}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Log preprocessing test failed: {e}")
        return False


def test_classification():
    """Test actual log classification"""
    print("\nTesting log classification...")
    
    try:
        import dspy
        from log_anomaly_classifier import LogAnomalyClassifier, LogPreprocessor
        from config import CURRENT_CONFIG as Config
        
        # Configure DSPy
        lm = dspy.LM(
            Config.MODEL_NAME,
            base_url=Config.get_ollama_url(),
            temperature=0.1
        )
        dspy.configure(lm=lm)
        
        # Create classifier
        classifier = LogAnomalyClassifier()
        preprocessor = LogPreprocessor()
        
        # Test with a sample log
        test_log = "- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected"
        entry = preprocessor.parse_log_line(test_log)
        
        if entry:
            result = classifier(
                log_message=entry.message,
                node_id=entry.node_id,
                severity=entry.severity,
                component=entry.component
            )
            
            print(f"✅ Classification successful:")
            print(f"   Predicted: {result.classification}")
            print(f"   Actual: {entry.label}")
            print(f"   Reasoning: {result.reasoning[:100]}...")
            return True
        else:
            print("❌ Could not parse test log")
            return False
            
    except Exception as e:
        print(f"❌ Classification test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🧪 Quick Test for DSPy Log Anomaly Classifier with Ollama")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Ollama Connection", test_ollama_connection),
        ("DSPy-Ollama Integration", test_dspy_ollama),
        ("Log Preprocessing", test_log_preprocessing),
        ("Log Classification", test_classification)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        print("\n📋 Next steps:")
        print("1. Run full training: python log_anomaly_classifier.py")
        print("2. Try demo: python demo_classifier.py")
    else:
        print("⚠️  Some tests failed. Please check the setup.")
        print("\n📋 Troubleshooting:")
        print("1. Make sure Ollama is running: ollama serve")
        print("2. Pull the model: ollama pull unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Q2_K_XL")
        print("3. Install dependencies: pip install -r requirements.txt")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
