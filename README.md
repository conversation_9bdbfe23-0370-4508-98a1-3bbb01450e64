# DSPy Log Anomaly Classification System

A sophisticated log anomaly detection system built with DSPy that classifies BGL (BlueGene/L) supercomputer log entries as normal or anomalous using the LabeledFewShot optimizer.

## Overview

This system uses DSPy (Declarative Self-improving Language Programs) to create an intelligent log anomaly classifier that can:

- Parse and preprocess BGL log entries
- Extract relevant features (timestamp, node ID, severity, component, message)
- Classify logs as "normal" (non-alert) or "anomalous" (alert) 
- Use few-shot learning with the LabeledFewShot optimizer
- Provide reasoning for classification decisions
- Evaluate performance with standard metrics

## Dataset

The system works with the BGL dataset from Lawrence Livermore National Labs:
- **Source**: BlueGene/L supercomputer system logs
- **Format**: Each line starts with "-" (normal) or other characters (anomalous)
- **Features**: Timestamp, node ID, component, severity, message content
- **Size**: Large-scale production logs from HPC environment

## Files

- `log_anomaly_classifier.py` - Main classifier implementation
- `demo_classifier.py` - Demo script with sample classifications
- `config.py` - Configuration settings
- `setup_ollama.py` - Automated Ollama setup script
- `quick_test.py` - Quick system verification script
- `test_system.py` - Comprehensive system testing
- `examine_bgl.py` - Dataset examination utility
- `requirements.txt` - Python dependencies
- `README.md` - This documentation

## Installation

1. Install Ollama:
```bash
# On Linux/macOS
curl -fsSL https://ollama.ai/install.sh | sh

# Or download from https://ollama.ai/
```

2. Start Ollama and pull the model:
```bash
ollama serve
ollama pull unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Q2_K_XL
```

3. Install Python dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### 0. Quick Setup and Test

For automated setup:
```bash
python setup_ollama.py
```

For quick testing:
```bash
python quick_test.py
```

### 1. Train the Classifier

Run the main classifier to train and evaluate the model:

```bash
python log_anomaly_classifier.py
```

This will:
- Load and preprocess the BGL dataset
- Create few-shot training examples
- Optimize the classifier using LabeledFewShot
- Evaluate performance on test data
- Save the trained model

### 2. Demo Classification

Run the demo with sample log entries:

```bash
python demo_classifier.py
```

For interactive classification:

```bash
python demo_classifier.py --interactive
```

### 3. Examine Dataset

To explore the BGL dataset structure:

```bash
python examine_bgl.py
```

## Configuration

Edit `config.py` to customize:

- **Dataset settings**: File path, sample size, train/test split
- **Model settings**: Ollama model name, base URL configuration
- **Training settings**: Few-shot examples, batch sizes
- **Output settings**: Save paths for models and results

### Configuration Classes

- `DevelopmentConfig`: Fast training with limited samples (default)
- `ProductionConfig`: Full dataset processing
- `Config`: Base configuration class

## Architecture

### Core Components

1. **LogPreprocessor**: Parses raw BGL log lines into structured components
2. **LogAnomalyClassifier**: DSPy module with ChainOfThought reasoning
3. **BGLDataset**: Dataset loader with train/test splitting
4. **LabeledFewShot Optimizer**: Optimizes classifier with labeled examples

### DSPy Signature

```python
class LogClassificationSignature(dspy.Signature):
    log_message = dspy.InputField(desc="The log message content to classify")
    node_id = dspy.InputField(desc="The node identifier where the log originated")
    severity = dspy.InputField(desc="The severity level of the log message")
    component = dspy.InputField(desc="The system component that generated the log")
    
    classification = dspy.OutputField(desc="Classification as 'normal' or 'anomalous'")
    reasoning = dspy.OutputField(desc="Brief explanation for the classification decision")
```

### Features Extracted

- **Label**: Normal ("-") vs Anomalous (other characters)
- **Timestamp**: When the log entry was created
- **Node ID**: Which compute node generated the log
- **Severity**: INFO, WARN, ERROR, FATAL, DEBUG
- **Component**: KERNEL, RAS, APP, MMCS, DISCOVERY, SYSTEM
- **Message**: Full log message content

## Performance Metrics

The system evaluates performance using:

- **Accuracy**: Overall classification correctness
- **Precision**: True positives / (True positives + False positives)
- **Recall**: True positives / (True positives + False negatives)
- **F1-Score**: Harmonic mean of precision and recall
- **Confusion Matrix**: Detailed breakdown of predictions

## Example Output

```
=== Evaluation Results ===
Accuracy: 0.892
Precision: 0.856
Recall: 0.923
F1-Score: 0.888
True Positives: 234
False Positives: 39
True Negatives: 658
False Negatives: 69
```

## Customization

### Adding New Features

1. Extend `LogPreprocessor` to extract additional features
2. Update `LogClassificationSignature` with new input fields
3. Modify the classifier forward pass to use new features

### Using Different Models

1. Update `config.py` with your preferred language model
2. Configure appropriate API keys and settings
3. Adjust few-shot examples and optimization parameters

### Dataset Adaptation

The system can be adapted for other log formats by:
1. Modifying `LogPreprocessor.parse_log_line()`
2. Updating feature extraction methods
3. Adjusting the classification signature

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure OPENAI_API_KEY is set correctly
2. **Dataset Not Found**: Check BGL_LOG_PATH in config.py
3. **Memory Issues**: Reduce MAX_SAMPLES in config for large datasets
4. **Model Loading**: Ensure trained model exists before running demo

### Performance Optimization

- Use `DevelopmentConfig` for faster iteration
- Adjust `MAX_SAMPLES` based on available memory
- Tune `FEW_SHOT_EXAMPLES` for better performance
- Consider using more powerful language models

## Citation

If you use this system in your research, please cite the original BGL dataset:

```
Adam J. Oliner, Jon Stearley. "What Supercomputers Say: A Study of Five System Logs", 
in Proc. of IEEE/IFIP International Conference on Dependable Systems and Networks (DSN), 2007.
```

## License

This project is provided as-is for educational and research purposes.
