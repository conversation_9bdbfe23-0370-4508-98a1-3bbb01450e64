#!/bin/bash

# BGL Log Deduplication Script
# This script removes duplicate log entries from the BGL.log file
# while preserving the original order and providing statistics

set -e  # Exit on any error

# Configuration
INPUT_FILE="datasets/BGL/BGL.log"
OUTPUT_FILE="datasets/BGL/BGL_deduplicated.log"
TEMP_DIR="/tmp/bgl_dedup_$$"
STATS_FILE="deduplication_stats.txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to format file size
format_size() {
    local size=$1
    if [ $size -gt 1073741824 ]; then
        echo "$(echo "scale=2; $size/1073741824" | bc)GB"
    elif [ $size -gt 1048576 ]; then
        echo "$(echo "scale=2; $size/1048576" | bc)MB"
    elif [ $size -gt 1024 ]; then
        echo "$(echo "scale=2; $size/1024" | bc)KB"
    else
        echo "${size}B"
    fi
}

# Function to clean up temporary files
cleanup() {
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
        print_status "Cleaned up temporary files"
    fi
}

# Set up cleanup on exit
trap cleanup EXIT

# Main deduplication function
deduplicate_bgl() {
    print_status "Starting BGL log deduplication..."
    
    # Check if input file exists
    if [ ! -f "$INPUT_FILE" ]; then
        print_error "Input file not found: $INPUT_FILE"
        exit 1
    fi
    
    # Create temporary directory
    mkdir -p "$TEMP_DIR"
    
    # Get initial statistics
    print_status "Analyzing input file..."
    local original_lines=$(wc -l < "$INPUT_FILE")
    local original_size=$(stat -c%s "$INPUT_FILE")
    
    print_status "Original file: $original_lines lines, $(format_size $original_size)"
    
    # Create output directory if it doesn't exist
    mkdir -p "$(dirname "$OUTPUT_FILE")"
    
    # Method 1: Deduplication based on content after 10th space
    print_status "Performing deduplication based on message content after 10th space..."

    # Use awk to preserve order while removing duplicates based on message content
    awk '{
        # Find the position after the 10th space
        spaces = 0
        pos = 1
        for (i = 1; i <= length($0); i++) {
            if (substr($0, i, 1) == " ") {
                spaces++
                if (spaces == 10) {
                    pos = i + 1
                    break
                }
            }
        }

        # Extract the message part (everything after 10th space)
        if (spaces >= 10) {
            message = substr($0, pos)
        } else {
            message = $0  # Use full line if less than 10 spaces
        }

        # Use first character (alert type) + message as deduplication key
        key = substr($0, 1, 1) " " message

        if (!seen[key]++) {
            print $0
        }
    }' "$INPUT_FILE" > "$OUTPUT_FILE"
    
    # Get final statistics
    local deduplicated_lines=$(wc -l < "$OUTPUT_FILE")
    local deduplicated_size=$(stat -c%s "$OUTPUT_FILE")
    local removed_lines=$((original_lines - deduplicated_lines))
    local size_reduction=$((original_size - deduplicated_size))
    
    # Calculate percentages
    local line_reduction_pct=$(echo "scale=2; $removed_lines * 100 / $original_lines" | bc)
    local size_reduction_pct=$(echo "scale=2; $size_reduction * 100 / $original_size" | bc)
    
    # Print results
    print_success "Deduplication completed!"
    echo
    echo "=== DEDUPLICATION STATISTICS ==="
    echo "Original lines:      $original_lines"
    echo "Deduplicated lines:  $deduplicated_lines"
    echo "Removed lines:       $removed_lines ($line_reduction_pct%)"
    echo
    echo "Original size:       $(format_size $original_size)"
    echo "Deduplicated size:   $(format_size $deduplicated_size)"
    echo "Size reduction:      $(format_size $size_reduction) ($size_reduction_pct%)"
    echo
    echo "Output file:         $OUTPUT_FILE"
    
    # Save statistics to file
    cat > "$STATS_FILE" << EOF
BGL Log Deduplication Statistics
Generated: $(date)

Input File: $INPUT_FILE
Output File: $OUTPUT_FILE

Original lines: $original_lines
Deduplicated lines: $deduplicated_lines
Removed lines: $removed_lines ($line_reduction_pct%)

Original size: $(format_size $original_size)
Deduplicated size: $(format_size $deduplicated_size)
Size reduction: $(format_size $size_reduction) ($size_reduction_pct%)
EOF
    
    print_status "Statistics saved to: $STATS_FILE"
}

# Function for advanced deduplication (message content only)
deduplicate_advanced() {
    print_status "Performing advanced deduplication (message content only)..."
    
    local advanced_output="datasets/BGL/BGL_deduplicated_advanced.log"
    
    # Extract message part and deduplicate based on that
    # BGL format: label timestamp date node_id time node_id_repeat message...
    # We'll deduplicate based on everything after the 6th field
    awk '{
        # Extract the message part (everything after field 6)
        msg = ""
        for(i=7; i<=NF; i++) {
            msg = msg $i " "
        }
        msg = substr(msg, 1, length(msg)-1)  # Remove trailing space
        
        # Use first character (label) + message as key
        key = $1 " " msg
        if (!seen[key]++) {
            print $0
        }
    }' "$INPUT_FILE" > "$advanced_output"
    
    local advanced_lines=$(wc -l < "$advanced_output")
    local advanced_size=$(stat -c%s "$advanced_output")
    local original_lines=$(wc -l < "$INPUT_FILE")
    local original_size=$(stat -c%s "$INPUT_FILE")
    
    local removed_lines=$((original_lines - advanced_lines))
    local line_reduction_pct=$(echo "scale=2; $removed_lines * 100 / $original_lines" | bc)
    
    print_success "Advanced deduplication completed!"
    echo "Advanced deduplicated lines: $advanced_lines"
    echo "Additional lines removed: $((deduplicated_lines - advanced_lines))"
    echo "Total reduction: $line_reduction_pct%"
    echo "Advanced output: $advanced_output"
}

# Function to analyze duplicate patterns
analyze_duplicates() {
    print_status "Analyzing duplicate patterns..."
    
    local analysis_file="duplicate_analysis.txt"
    
    # Find most common duplicate lines
    print_status "Finding most common duplicate lines..."
    sort "$INPUT_FILE" | uniq -c | sort -nr | head -20 > "$TEMP_DIR/top_duplicates.txt"
    
    # Analyze by log type (first character)
    print_status "Analyzing by log type..."
    cut -c1 "$INPUT_FILE" | sort | uniq -c | sort -nr > "$TEMP_DIR/log_types.txt"
    
    # Create analysis report
    cat > "$analysis_file" << EOF
BGL Log Duplicate Analysis
Generated: $(date)

=== TOP 20 MOST FREQUENT DUPLICATE LINES ===
$(cat "$TEMP_DIR/top_duplicates.txt")

=== LOG TYPE DISTRIBUTION ===
$(cat "$TEMP_DIR/log_types.txt")

Legend:
- : Normal/non-alert messages
Other characters: Alert/anomalous messages
EOF
    
    print_success "Duplicate analysis saved to: $analysis_file"
}

# Function to create balanced dataset
create_balanced_dataset() {
    print_status "Creating balanced dataset..."
    
    local balanced_output="datasets/BGL/BGL_balanced.log"
    local normal_count=1000
    local anomalous_count=1000
    
    # Extract normal entries (starting with -)
    grep "^-" "$OUTPUT_FILE" | head -n $normal_count > "$TEMP_DIR/normal.log"
    
    # Extract anomalous entries (not starting with -)
    grep -v "^-" "$OUTPUT_FILE" | head -n $anomalous_count > "$TEMP_DIR/anomalous.log"
    
    # Combine and shuffle
    cat "$TEMP_DIR/normal.log" "$TEMP_DIR/anomalous.log" | shuf > "$balanced_output"
    
    local actual_normal=$(wc -l < "$TEMP_DIR/normal.log")
    local actual_anomalous=$(wc -l < "$TEMP_DIR/anomalous.log")
    local total_balanced=$(wc -l < "$balanced_output")
    
    print_success "Balanced dataset created!"
    echo "Normal entries: $actual_normal"
    echo "Anomalous entries: $actual_anomalous"
    echo "Total entries: $total_balanced"
    echo "Balanced output: $balanced_output"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -a, --advanced      Perform advanced deduplication (message content only)"
    echo "  -b, --balanced      Create balanced dataset after deduplication"
    echo "  -A, --analyze       Analyze duplicate patterns"
    echo "  --all               Perform all operations"
    echo
    echo "Examples:"
    echo "  $0                  # Basic deduplication"
    echo "  $0 --advanced       # Advanced deduplication"
    echo "  $0 --balanced       # Create balanced dataset"
    echo "  $0 --all            # Perform all operations"
}

# Parse command line arguments
ADVANCED=false
BALANCED=false
ANALYZE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -a|--advanced)
            ADVANCED=true
            shift
            ;;
        -b|--balanced)
            BALANCED=true
            shift
            ;;
        -A|--analyze)
            ANALYZE=true
            shift
            ;;
        --all)
            ADVANCED=true
            BALANCED=true
            ANALYZE=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "=== BGL Log Deduplication Script ==="
    echo
    
    # Check dependencies
    if ! command -v bc &> /dev/null; then
        print_error "bc calculator not found. Please install: sudo apt-get install bc"
        exit 1
    fi
    
    # Perform basic deduplication
    deduplicate_bgl
    
    # Perform advanced deduplication if requested
    if [ "$ADVANCED" = true ]; then
        echo
        deduplicate_advanced
    fi
    
    # Analyze duplicates if requested
    if [ "$ANALYZE" = true ]; then
        echo
        analyze_duplicates
    fi
    
    # Create balanced dataset if requested
    if [ "$BALANCED" = true ]; then
        echo
        create_balanced_dataset
    fi
    
    echo
    print_success "All operations completed successfully!"
    
    # Show next steps
    echo
    echo "=== NEXT STEPS ==="
    echo "1. Use deduplicated file in your classifier:"
    echo "   Update config.py: BGL_LOG_PATH = '$OUTPUT_FILE'"
    echo
    if [ "$BALANCED" = true ]; then
        echo "2. Or use balanced dataset for training:"
        echo "   Update config.py: BGL_LOG_PATH = 'datasets/BGL/BGL_balanced.log'"
        echo
    fi
    echo "3. Run the classifier:"
    echo "   python log_anomaly_classifier.py"
}

# Run main function
main "$@"
