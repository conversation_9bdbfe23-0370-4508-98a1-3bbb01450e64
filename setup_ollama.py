#!/usr/bin/env python3
"""
Setup script for Ollama and the Qwen3 model for log anomaly classification
"""

import os
import sys
import time
import subprocess
import requests
from typing import Optional


def check_ollama_installed() -> bool:
    """Check if Ollama is installed"""
    try:
        result = subprocess.run(["ollama", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Ollama is installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ Ollama is not installed")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Ollama is not installed")
        return False


def check_ollama_running() -> bool:
    """Check if Ollama service is running"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama service is running")
            return True
        else:
            print("❌ Ollama service is not responding")
            return False
    except requests.exceptions.RequestException:
        print("❌ Ollama service is not running")
        return False


def start_ollama_service() -> bool:
    """Start Ollama service"""
    print("Starting Ollama service...")
    try:
        # Start ollama serve in background
        process = subprocess.Popen(
            ["ollama", "serve"],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        # Wait a bit for service to start
        time.sleep(3)
        
        # Check if it's running
        if check_ollama_running():
            print("✅ Ollama service started successfully")
            return True
        else:
            print("❌ Failed to start Ollama service")
            return False
            
    except Exception as e:
        print(f"❌ Error starting Ollama service: {e}")
        return False


def check_model_available(model_name: str) -> bool:
    """Check if the specified model is available"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]
            
            # Check if our target model is available
            if any(model_name in name for name in model_names):
                print(f"✅ Model {model_name} is available")
                return True
            else:
                print(f"❌ Model {model_name} is not available")
                print(f"Available models: {model_names}")
                return False
        else:
            print("❌ Could not check available models")
            return False
    except Exception as e:
        print(f"❌ Error checking models: {e}")
        return False


def pull_model(model_name: str) -> bool:
    """Pull the specified model"""
    print(f"Pulling model {model_name}...")
    print("This may take a while depending on your internet connection...")
    
    try:
        # Run ollama pull command
        result = subprocess.run(
            ["ollama", "pull", model_name],
            capture_output=True,
            text=True,
            timeout=1800  # 30 minutes timeout
        )
        
        if result.returncode == 0:
            print(f"✅ Successfully pulled model {model_name}")
            return True
        else:
            print(f"❌ Failed to pull model {model_name}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Model pull timed out (30 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error pulling model: {e}")
        return False


def test_model(model_name: str) -> bool:
    """Test the model with a simple query"""
    print(f"Testing model {model_name}...")
    
    try:
        # Simple test query
        test_prompt = "Classify this log entry as normal or anomalous: INFO: System started successfully"
        
        result = subprocess.run(
            ["ollama", "run", model_name, test_prompt],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print(f"✅ Model {model_name} is working correctly")
            print(f"Test response: {result.stdout.strip()[:100]}...")
            return True
        else:
            print(f"❌ Model {model_name} test failed")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Model test timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False


def install_python_dependencies() -> bool:
    """Install Python dependencies"""
    print("Installing Python dependencies...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✅ Python dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install Python dependencies")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Dependency installation timed out")
        return False
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def main():
    """Main setup function"""
    print("🚀 Setting up Ollama and Qwen3 model for Log Anomaly Classification")
    print("=" * 70)
    
    model_name = "unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF:Q2_K_XL"
    
    # Step 1: Check if Ollama is installed
    if not check_ollama_installed():
        print("\n📥 Please install Ollama first:")
        print("   curl -fsSL https://ollama.ai/install.sh | sh")
        print("   Or download from: https://ollama.ai/")
        return False
    
    # Step 2: Check if Ollama is running, start if needed
    if not check_ollama_running():
        if not start_ollama_service():
            print("\n🔧 Please start Ollama manually:")
            print("   ollama serve")
            return False
    
    # Step 3: Check if model is available, pull if needed
    if not check_model_available(model_name):
        print(f"\n📦 Pulling model {model_name}...")
        if not pull_model(model_name):
            return False
    
    # Step 4: Test the model
    if not test_model(model_name):
        print("\n⚠️  Model test failed, but continuing...")
    
    # Step 5: Install Python dependencies
    if not install_python_dependencies():
        print("\n⚠️  Python dependency installation failed")
        return False
    
    # Step 6: Run system test
    print("\n🧪 Running system tests...")
    try:
        result = subprocess.run(
            [sys.executable, "test_system.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        print(result.stdout)
        if result.stderr:
            print("Warnings/Errors:")
            print(result.stderr)
    except Exception as e:
        print(f"Could not run system tests: {e}")
    
    print("\n🎉 Setup complete!")
    print("\n📋 Next steps:")
    print("1. Run the classifier: python log_anomaly_classifier.py")
    print("2. Try the demo: python demo_classifier.py")
    print("3. Interactive mode: python demo_classifier.py --interactive")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
